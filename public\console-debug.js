// 在浏览器控制台中运行的调试脚本
// 复制粘贴到主应用程序页面的控制台中执行

console.log('=== 3D模型调试脚本开始 ===');

// 检查Vue应用实例
if (typeof window !== 'undefined' && window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('Vue DevTools 可用');
} else {
    console.log('Vue DevTools 不可用');
}

// 检查全局变量
console.log('检查全局变量:');
console.log('- AMap:', typeof AMap !== 'undefined' ? AMap.version : '未定义');
console.log('- Vue:', typeof Vue !== 'undefined' ? Vue.version : '未定义');

// 尝试获取地图实例
function findMapInstance() {
    // 尝试从DOM中找到地图容器
    const mapContainer = document.getElementById('yunnan-map');
    if (mapContainer) {
        console.log('找到地图容器:', mapContainer);
        
        // 尝试从容器中获取地图实例
        if (mapContainer._amap_instance) {
            return mapContainer._amap_instance;
        }
        
        // 尝试其他可能的属性
        const possibleProps = ['map', '_map', 'amapInstance', '_amapInstance'];
        for (const prop of possibleProps) {
            if (mapContainer[prop]) {
                console.log(`从容器属性 ${prop} 找到地图实例`);
                return mapContainer[prop];
            }
        }
    }
    
    // 尝试从全局变量中找到
    if (typeof window.mapInstance !== 'undefined') {
        console.log('从全局变量找到地图实例');
        return window.mapInstance;
    }
    
    return null;
}

const map = findMapInstance();
if (map) {
    console.log('✓ 找到地图实例:', map);
    console.log('- 当前缩放级别:', map.getZoom());
    console.log('- 当前中心点:', map.getCenter());
    console.log('- 视图模式:', map.getViewMode ? map.getViewMode() : (map.getViewMode_ ? map.getViewMode_() : '未知'));
    
    // 检查图层
    const layers = map.getLayers();
    console.log('- 图层数量:', layers.length);
    layers.forEach((layer, index) => {
        console.log(`  图层 ${index}:`, layer.CLASS_NAME || layer.constructor.name);
    });
    
    // 检查3D对象图层
    const object3DLayers = layers.filter(layer => 
        layer.CLASS_NAME === 'AMap.Object3DLayer' || 
        layer.constructor.name === 'Object3DLayer'
    );
    console.log('- 3D对象图层数量:', object3DLayers.length);
    
    if (object3DLayers.length > 0) {
        object3DLayers.forEach((layer, index) => {
            console.log(`  3D图层 ${index}:`, layer);
            if (layer.getAllOverlays) {
                const overlays = layer.getAllOverlays();
                console.log(`    - 3D对象数量: ${overlays ? overlays.length : 0}`);
            }
        });
    }
    
} else {
    console.log('✗ 未找到地图实例');
}

// 检查GLTF文件访问
console.log('检查GLTF文件访问:');
fetch('/assets/model/dt.gltf')
    .then(response => {
        console.log('- GLTF文件响应状态:', response.status);
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    })
    .then(gltfData => {
        console.log('✓ GLTF文件解析成功');
        console.log('- 资产信息:', gltfData.asset);
        console.log('- 场景数量:', gltfData.scenes ? gltfData.scenes.length : 0);
        console.log('- 节点数量:', gltfData.nodes ? gltfData.nodes.length : 0);
        console.log('- 网格数量:', gltfData.meshes ? gltfData.meshes.length : 0);
    })
    .catch(error => {
        console.log('✗ GLTF文件访问失败:', error.message);
    });

// 检查高德地图API
if (typeof AMap !== 'undefined') {
    console.log('检查高德地图API:');
    console.log('- AMap版本:', AMap.version);
    console.log('- GltfLoader可用:', !!AMap.GltfLoader);
    console.log('- Object3DLayer可用:', !!AMap.Object3DLayer);
    console.log('- Object3D可用:', !!AMap.Object3D);
    
    if (AMap.Object3D) {
        console.log('- Object3D子类:');
        console.log('  - Mesh:', !!AMap.Object3D.Mesh);
        console.log('  - BoxGeometry:', !!AMap.Object3D.BoxGeometry);
        console.log('  - MeshBasicMaterial:', !!AMap.Object3D.MeshBasicMaterial);
    }
    
    // 测试GLTF加载器
    if (AMap.GltfLoader) {
        console.log('测试GLTF加载器:');
        const testLoader = new AMap.GltfLoader();
        console.log('- 加载器实例创建成功:', !!testLoader);
        
        // 尝试加载模型（不添加到地图）
        testLoader.load('/assets/model/dt.gltf', 
            function(gltfModel) {
                console.log('✓ 测试GLTF模型加载成功:', gltfModel);
            },
            function(error) {
                console.log('✗ 测试GLTF模型加载失败:', error);
            }
        );
    }
}

// 提供一些有用的调试函数
window.debugMap = {
    getMapInfo: function() {
        const map = findMapInstance();
        if (map) {
            return {
                zoom: map.getZoom(),
                center: map.getCenter(),
                viewMode: map.getViewMode ? map.getViewMode() : (map.getViewMode_ ? map.getViewMode_() : '未知'),
                layers: map.getLayers().length
            };
        }
        return null;
    },
    
    switchTo3D: function() {
        const map = findMapInstance();
        if (map) {
            map.setViewMode('3D');
            map.setPitch(45);
            console.log('已切换到3D模式');
        }
    },
    
    zoomTo: function(level) {
        const map = findMapInstance();
        if (map) {
            map.setZoom(level);
            console.log(`已设置缩放级别为: ${level}`);
        }
    },
    
    testGltfLoad: function() {
        if (typeof AMap !== 'undefined' && AMap.GltfLoader) {
            const loader = new AMap.GltfLoader();
            loader.load('/assets/model/dt.gltf',
                function(model) {
                    console.log('✓ GLTF加载测试成功:', model);
                },
                function(error) {
                    console.log('✗ GLTF加载测试失败:', error);
                }
            );
        } else {
            console.log('GltfLoader不可用');
        }
    }
};

console.log('=== 调试脚本完成 ===');
console.log('可用的调试函数:');
console.log('- debugMap.getMapInfo() - 获取地图信息');
console.log('- debugMap.switchTo3D() - 切换到3D模式');
console.log('- debugMap.zoomTo(level) - 设置缩放级别');
console.log('- debugMap.testGltfLoad() - 测试GLTF加载');
