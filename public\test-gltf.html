<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GLTF模型加载测试</title>
    <style>
        #container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .status {
            margin: 5px 0;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>GLTF模型加载测试</h1>
    
    <div class="info">
        <h3>测试结果：</h3>
        <div id="test-status"></div>
    </div>
    
    <div id="container"></div>

    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=51302baed58ef2d9c48e713230aee1a4&plugin=AMap.GltfLoader,Map3D"></script>
    <script>
        function testGltfLoading() {
            const statusDiv = document.getElementById('test-status');
            let html = '';
            
            // 检查基础API
            html += `<div class="status ${AMap ? 'success' : 'error'}">AMap: ${!!AMap}</div>`;
            html += `<div class="status">AMap版本: ${AMap ? AMap.version : '未加载'}</div>`;
            
            // 检查GLTF相关API
            if (AMap) {
                html += `<div class="status ${AMap.GltfLoader ? 'success' : 'error'}">GltfLoader: ${!!AMap.GltfLoader}</div>`;
                html += `<div class="status ${AMap.Object3DLayer ? 'success' : 'error'}">Object3DLayer: ${!!AMap.Object3DLayer}</div>`;
            }
            
            statusDiv.innerHTML = html;
            
            if (!AMap || !AMap.GltfLoader) {
                statusDiv.innerHTML += '<div class="status error">无法进行GLTF测试</div>';
                return;
            }
            
            // 创建地图
            const map = new AMap.Map("container", {
                viewMode: '3D',
                pitch: 30,
                rotation: 25,
                zoom: 16,
                center: [101.5, 25.0], // 云南省中心
                showBuildingBlock: false,
                mapStyle: 'amap://styles/normal'
            });
            
            // 创建Object3DLayer图层
            const object3Dlayer = new AMap.Object3DLayer();
            map.add(object3Dlayer);
            
            map.on('complete', function() {
                console.log('地图加载完成，开始测试GLTF加载');
                statusDiv.innerHTML += '<div class="status">地图加载完成，开始测试GLTF加载...</div>';
                
                // 测试GLTF模型加载
                const gltfLoader = new AMap.GltfLoader();
                const modelUrl = '/assets/model/dt.gltf';
                
                console.log('尝试加载GLTF模型:', modelUrl);
                statusDiv.innerHTML += `<div class="status">尝试加载模型: ${modelUrl}</div>`;
                
                gltfLoader.load(modelUrl, function(gltfModel) {
                    console.log('GLTF模型加载成功!', gltfModel);
                    statusDiv.innerHTML += '<div class="status success">✓ GLTF模型加载成功!</div>';
                    
                    // 设置模型参数
                    const modelOptions = {
                        position: new AMap.LngLat(101.5, 25.0),
                        scale: 1000,
                        height: 0,
                        scene: 0,
                    };
                    
                    gltfModel.setOption(modelOptions);
                    gltfModel.rotateX(90);
                    gltfModel.rotateZ(10);
                    
                    // 添加到图层
                    object3Dlayer.add(gltfModel);
                    
                    statusDiv.innerHTML += '<div class="status success">✓ 模型已添加到地图!</div>';
                    
                }, function(error) {
                    console.error('GLTF模型加载失败:', error);
                    statusDiv.innerHTML += `<div class="status error">✗ GLTF模型加载失败: ${error.message || error}</div>`;
                });
            });
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            testGltfLoading();
        };
    </script>
</body>
</html>
