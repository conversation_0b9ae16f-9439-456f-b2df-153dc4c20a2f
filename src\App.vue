<script setup>
import { ref } from 'vue'
import YunNanMap from './view/YunNanMap.vue'


// 控制测试模式
const testMode = ref(false)
</script>

<template>
  <div class="app">

    <!-- 正常模式：显示地图页面 -->
    <YunNanMap />
  </div>
</template>

<style>
/* 全局样式重置 - 移除scoped以确保全局生效 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

/* 测试按钮样式 */
.test-toggle-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999;
}
</style>
