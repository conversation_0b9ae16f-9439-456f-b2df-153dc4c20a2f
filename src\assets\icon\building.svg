<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="building10Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffa940;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#fa8c16;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#d46b08;stop-opacity:0.8" />
    </linearGradient>
    <filter id="building10Shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="1.5" flood-color="rgba(0,0,0,0.4)"/>
    </filter>
  </defs>
  
  <!-- 主建筑体 -->
  <rect x="4" y="8" width="12" height="10" fill="url(#building10Gradient)" filter="url(#building10Shadow)"/>
  <!-- 立体屋顶 -->
  <polygon points="4,8 2,6 18,6 16,8" fill="#ffa940" filter="url(#building10Shadow)"/>
  <!-- 立体侧面 -->
  <polygon points="16,8 18,6 18,16 16,18" fill="#d46b08" filter="url(#building10Shadow)"/>
  
  <!-- 屋顶结构 -->
  <polygon points="2,6 10,2 18,6 10,4 2,6" fill="#ffbb96" filter="url(#building10Shadow)"/>
  
  <!-- 窗户 -->
  <rect x="6" y="10" width="2" height="2" fill="#1890ff" opacity="0.8"/>
  <rect x="12" y="10" width="2" height="2" fill="#1890ff" opacity="0.8"/>
  <rect x="6" y="14" width="2" height="2" fill="#1890ff" opacity="0.8"/>
  <rect x="12" y="14" width="2" height="2" fill="#1890ff" opacity="0.8"/>
  
  <!-- 门 -->
  <rect x="9" y="13" width="2" height="5" fill="#8c4a2f"/>
  
  <!-- 电力连接线 -->
  <line x1="10" y1="2" x2="10" y2="0.5" stroke="#595959" stroke-width="1"/>
  <circle cx="10" cy="0.5" r="0.5" fill="#fa8c16"/>
</svg>
