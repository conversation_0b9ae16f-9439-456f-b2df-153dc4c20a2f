<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 3D渐变效果 -->
    <linearGradient id="tower500Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff7875;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff4d4f;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#cf1322;stop-opacity:0.8" />
    </linearGradient>
    <!-- 阴影滤镜 -->
    <filter id="tower500Shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="1.5" flood-color="rgba(0,0,0,0.4)"/>
    </filter>
  </defs>
  
  <!-- 塔身主体 -->
  <rect x="15" y="6" width="2" height="20" fill="url(#tower500Gradient)" filter="url(#tower500Shadow)"/>
  
  <!-- 塔顶 -->
  <path d="M16 2L13 6H19L16 2Z" fill="url(#tower500Gradient)" filter="url(#tower500Shadow)"/>
  
  <!-- 横梁结构 - 立体效果 -->
  <path d="M8 10L12 8L20 8L24 10L20 12L12 12L8 10Z" fill="url(#tower500Gradient)" filter="url(#tower500Shadow)"/>
  <path d="M8 10L9 9L21 9L24 10L21 11L9 11L8 10Z" fill="#ff7875" opacity="0.7"/>
  
  <path d="M10 16L14 14L18 14L22 16L18 18L14 18L10 16Z" fill="url(#tower500Gradient)" filter="url(#tower500Shadow)"/>
  <path d="M10 16L11 15L19 15L22 16L19 17L11 17L10 16Z" fill="#ff7875" opacity="0.7"/>
  
  <path d="M12 22L16 20L20 22L16 24L12 22Z" fill="url(#tower500Gradient)" filter="url(#tower500Shadow)"/>
  <path d="M12 22L13 21L19 21L20 22L19 23L13 23L12 22Z" fill="#ff7875" opacity="0.7"/>
  
  <!-- 绝缘子 - 3D球体效果 -->
  <circle cx="8" cy="10" r="1.5" fill="#ff7875" filter="url(#tower500Shadow)"/>
  <circle cx="8" cy="10" r="1" fill="#ffa39e"/>
  <circle cx="24" cy="10" r="1.5" fill="#ff7875" filter="url(#tower500Shadow)"/>
  <circle cx="24" cy="10" r="1" fill="#ffa39e"/>
  <circle cx="10" cy="16" r="1.5" fill="#ff7875" filter="url(#tower500Shadow)"/>
  <circle cx="10" cy="16" r="1" fill="#ffa39e"/>
  <circle cx="22" cy="16" r="1.5" fill="#ff7875" filter="url(#tower500Shadow)"/>
  <circle cx="22" cy="16" r="1" fill="#ffa39e"/>
</svg>
