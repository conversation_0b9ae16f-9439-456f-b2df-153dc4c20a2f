<template>
  <div class="detail-panel" v-show="visible">
    <div class="panel-header">
      <div class="header-content">
        <el-icon class="header-icon">
          <Connection v-if="type === 'powerline'" />
          <Connection v-else-if="type === 'tower'" />
          <OfficeBuilding v-else-if="type === 'substation'" />
          <OfficeBuilding v-else-if="type === 'powerPlant'" />
        </el-icon>
        <h3 class="panel-title">{{ getTitle() }}</h3>
      </div>
      <el-button 
        class="close-btn" 
        :icon="Close" 
        size="small" 
        text 
        @click="handleClose"
      />
    </div>
    
    <div class="panel-content">
      <!-- 线路详细信息 -->
      <div v-if="type === 'powerline'" class="powerline-details">
        <div class="info-group">
          <div class="group-header">
            <el-icon><InfoFilled /></el-icon>
            <label>基本信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">线路名称:</span>
              <span class="value">{{ data.lineName }}</span>
            </div>
            <div class="info-item">
              <span class="label">电压等级:</span>
              <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
                {{ data.voltageLevel }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">线路级别:</span>
              <span class="value level-badge" :class="getLevelBadgeClass(data.lineLevel)">
                {{ getLevelName(data.lineLevel) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">线路长度:</span>
              <span class="value">{{ data.length }}km</span>
            </div>
            <div class="info-item">
              <span class="label">建设状态:</span>
              <span class="value status-badge" :class="getStatusBadgeClass(data.status)">
                {{ data.status }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 已完成线路显示详细信息 -->
        <div v-if="data.status === '已完成'" class="completed-details">
          <div class="info-group" v-if="data.detailInfo">
            <div class="group-header">
              <el-icon><Setting /></el-icon>
              <label>技术参数</label>
            </div>
            <div class="info-grid">
              <div class="info-item" v-if="data.detailInfo.conductorType">
                <span class="label">导线型号:</span>
                <span class="value">{{ data.detailInfo.conductorType }}</span>
              </div>
              <div class="info-item" v-if="data.detailInfo.ratedCurrent">
                <span class="label">额定电流:</span>
                <span class="value">{{ data.detailInfo.ratedCurrent }}A</span>
              </div>
              <div class="info-item" v-if="data.detailInfo.capacity">
                <span class="label">输送容量:</span>
                <span class="value">{{ data.detailInfo.capacity }}MW</span>
              </div>
            </div>
          </div>

          <div class="info-group" v-if="data.detailInfo">
            <div class="group-header">
              <el-icon><Document /></el-icon>
              <label>建设信息</label>
            </div>
            <div class="info-grid">
              <div class="info-item" v-if="data.detailInfo.constructor">
                <span class="label">建设单位:</span>
                <span class="value">{{ data.detailInfo.constructor }}</span>
              </div>
              <div class="info-item" v-if="data.detailInfo.operationDate">
                <span class="label">投运时间:</span>
                <span class="value">{{ data.detailInfo.operationDate }}</span>
              </div>
              <div class="info-item" v-if="data.detailInfo.maintainer">
                <span class="label">维护单位:</span>
                <span class="value">{{ data.detailInfo.maintainer }}</span>
              </div>
            </div>
          </div>

          <div class="info-group" v-if="data.detailInfo && data.detailInfo.currentLoad !== undefined">
            <div class="group-header">
              <el-icon><DataAnalysis /></el-icon>
              <label>运行数据</label>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">当前负载:</span>
                <span class="value">{{ data.detailInfo.currentLoad }}MW</span>
              </div>
              <div class="info-item">
                <span class="label">负载率:</span>
                <span class="value">{{ data.detailInfo.loadRate }}%</span>
              </div>
              <div class="info-item">
                <span class="label">最后维护:</span>
                <span class="value">{{ data.detailInfo.lastMaintenance }}</span>
              </div>
              <div class="info-item">
                <span class="label">维护状态:</span>
                <span class="value">{{ data.detailInfo.maintenanceStatus }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 建设中和规划中线路显示简单信息 -->
        <div v-else-if="data.status === '建设中' || data.status === '规划中'" class="simple-details">
          <div class="info-group">
            <div class="group-header">
              <el-icon><Document /></el-icon>
              <label>项目信息</label>
            </div>
            <div class="info-grid">
              <div class="info-item" v-if="data.constructionDate">
                <span class="label">开工时间:</span>
                <span class="value">{{ data.constructionDate }}</span>
              </div>
              <div class="info-item" v-if="data.expectedOperationDate">
                <span class="label">预计投运:</span>
                <span class="value">{{ data.expectedOperationDate }}</span>
              </div>
              <div class="info-item" v-if="data.capacity">
                <span class="label">设计容量:</span>
                <span class="value">{{ data.capacity }}</span>
              </div>
              <div class="info-item" v-if="data.description">
                <span class="label">项目描述:</span>
                <span class="value">{{ data.description }}</span>
              </div>
            </div>
          </div>

          <!-- 建设中线路显示进度条 -->
          <div v-if="data.status === '建设中'" class="info-group">
            <div class="group-header">
              <el-icon><Loading /></el-icon>
              <label>建设进度</label>
            </div>
            <div class="progress-container">
              <div class="progress-info">
                <span class="progress-label">整体进度</span>
                <span class="progress-percentage">{{ getConstructionProgress() }}%</span>
              </div>
              <el-progress
                :percentage="getConstructionProgress()"
                :stroke-width="8"
                :color="getProgressColor()"
                class="construction-progress"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 电塔详细信息 -->
      <div v-else-if="type === 'tower'" class="tower-details">
        <div class="info-group">
          <div class="group-header">
            <el-icon><InfoFilled /></el-icon>
            <label>基本信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">电塔编号:</span>
              <span class="value">{{ data.towerId }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo && data.detailInfo.towerType">
              <span class="label">电塔类型:</span>
              <span class="value">{{ data.detailInfo.towerType }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属线路:</span>
              <span class="value">{{ data.powerlineName }}</span>
            </div>
            <div class="info-item">
              <span class="label">电压等级:</span>
              <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
                {{ data.voltageLevel }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><Location /></el-icon>
            <label>位置信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">经度:</span>
              <span class="value">{{ data.detailInfo.longitude }}°</span>
            </div>
            <div class="info-item">
              <span class="label">纬度:</span>
              <span class="value">{{ data.detailInfo.latitude }}°</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.altitude">
              <span class="label">海拔高度:</span>
              <span class="value">{{ data.detailInfo.altitude }}m</span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><Setting /></el-icon>
            <label>技术规格</label>
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="data.detailInfo.height">
              <span class="label">塔高:</span>
              <span class="value">{{ data.detailInfo.height }}m</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.foundationType">
              <span class="label">基础类型:</span>
              <span class="value">{{ data.detailInfo.foundationType }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.maintenanceStatus">
              <span class="label">维护状态:</span>
              <span class="value">{{ data.detailInfo.maintenanceStatus }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 变电站详细信息 -->
      <div v-else-if="type === 'substation'" class="substation-details">
        <div class="info-group">
          <div class="group-header">
            <el-icon><InfoFilled /></el-icon>
            <label>基本信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">变电站名称:</span>
              <span class="value">{{ data.stationName }}</span>
            </div>
            <div class="info-item">
              <span class="label">电压等级:</span>
              <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
                {{ data.voltageLevel }}
              </span>
            </div>
            <div class="info-item" v-if="data.detailInfo && data.detailInfo.substationType">
              <span class="label">变电站类型:</span>
              <span class="value">{{ data.detailInfo.substationType }}</span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><DataAnalysis /></el-icon>
            <label>容量信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="data.detailInfo.transformerCapacity">
              <span class="label">主变容量:</span>
              <span class="value">{{ data.detailInfo.transformerCapacity }}MVA</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.incomingLines">
              <span class="label">进线回数:</span>
              <span class="value">{{ data.detailInfo.incomingLines }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.outgoingLines">
              <span class="label">出线回数:</span>
              <span class="value">{{ data.detailInfo.outgoingLines }}</span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><Monitor /></el-icon>
            <label>运行状态</label>
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="data.detailInfo.operationStatus">
              <span class="label">运行状态:</span>
              <span class="value">{{ data.detailInfo.operationStatus }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.loadRate">
              <span class="label">负载率:</span>
              <span class="value">{{ data.detailInfo.loadRate }}%</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.lastMaintenance">
              <span class="label">最后检修:</span>
              <span class="value">{{ data.detailInfo.lastMaintenance }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 发电站详细信息 -->
      <div v-else-if="type === 'powerPlant'" class="powerplant-details">
        <div class="info-group">
          <div class="group-header">
            <el-icon><InfoFilled /></el-icon>
            <label>基本信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">发电站名称:</span>
              <span class="value">{{ data.plantName }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo && data.detailInfo.generationType">
              <span class="label">发电类型:</span>
              <span class="value">{{ getGenerationTypeName(data.detailInfo.generationType) }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo && data.detailInfo.company">
              <span class="label">所属公司:</span>
              <span class="value">{{ data.detailInfo.company }}</span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><Setting /></el-icon>
            <label>装机信息</label>
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="data.detailInfo.installedCapacity">
              <span class="label">装机容量:</span>
              <span class="value">{{ data.detailInfo.installedCapacity }}MW</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.unitCount">
              <span class="label">机组数量:</span>
              <span class="value">{{ data.detailInfo.unitCount }}</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.commissionDate">
              <span class="label">投产时间:</span>
              <span class="value">{{ data.detailInfo.commissionDate }}</span>
            </div>
          </div>
        </div>
        
        <div class="info-group" v-if="data.detailInfo">
          <div class="group-header">
            <el-icon><DataAnalysis /></el-icon>
            <label>运行数据</label>
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="data.detailInfo.currentOutput">
              <span class="label">当前出力:</span>
              <span class="value">{{ data.detailInfo.currentOutput }}MW</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.annualGeneration">
              <span class="label">年发电量:</span>
              <span class="value">{{ data.detailInfo.annualGeneration }}GWh</span>
            </div>
            <div class="info-item" v-if="data.detailInfo.operationStatus">
              <span class="label">运行状态:</span>
              <span class="value">{{ data.detailInfo.operationStatus }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Close, Connection, OfficeBuilding,
  InfoFilled, Setting, Document, Loading
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  },
  type: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['close'])

// 获取面板标题
const getTitle = () => {
  if (!props.data) return ''
  
  switch (props.type) {
    case 'powerline':
      return props.data.lineName || '电力线路'
    case 'tower':
      return `电塔 ${props.data.towerId || ''}`
    case 'substation':
      return props.data.stationName || '变电站'
    case 'powerPlant':
      return props.data.plantName || '发电站'
    default:
      return '详细信息'
  }
}

// 获取电压等级徽章样式类
const getVoltageBadgeClass = (voltageLevel) => {
  const classMap = {
    '500KV': 'voltage-500kv',
    '220KV': 'voltage-220kv',
    '110KV': 'voltage-110kv',
    '35KV': 'voltage-35kv',
    '10KV': 'voltage-10kv'
  }
  return classMap[voltageLevel] || 'voltage-default'
}

// 获取状态徽章样式类
const getStatusBadgeClass = (status) => {
  const classMap = {
    '已完成': 'status-completed',
    '建设中': 'status-construction',
    '规划中': 'status-planning',
    '检修中': 'status-maintenance',
    '停运': 'status-shutdown'
  }
  return classMap[status] || 'status-default'
}

// 获取线路级别徽章样式类
const getLevelBadgeClass = (level) => {
  // 如果级别为空、未定义或null，返回其他样式
  if (!level || level.trim() === '') {
    return 'level-other'
  }

  const classMap = {
    '国际': 'level-international',
    '国重': 'level-national',
    '省重': 'level-provincial',
    '战略': 'level-strategic',
    '应急': 'level-emergency',
    '民生': 'level-livelihood',
    '其他': 'level-other'
  }
  return classMap[level] || 'level-other'
}

// 获取线路级别名称
const getLevelName = (level) => {
  // 如果级别为空、未定义或null，返回"其他项目"
  if (!level || level.trim() === '') {
    return '其他项目'
  }

  const nameMap = {
    '国际': '国际合作项目',
    '国重': '国家重点项目',
    '省重': '省重点项目',
    '战略': '战略项目',
    '应急': '应急保障',
    '民生': '民生工程',
    '其他': '其他项目'
  }
  return nameMap[level] || '其他项目'
}

// 获取发电类型名称
const getGenerationTypeName = (type) => {
  const nameMap = {
    'thermal': '火力发电',
    'hydro': '水力发电',
    'wind': '风力发电',
    'solar': '太阳能发电'
  }
  return nameMap[type] || type
}

// 获取建设进度百分比
const getConstructionProgress = () => {
  if (!props.data) return 0

  // 如果有直接的进度数据，使用它
  if (props.data.constructionProgress !== undefined) {
    return props.data.constructionProgress
  }

  // 如果有分段数据，计算平均进度
  if (props.data.segments && props.data.segments.length > 0) {
    let totalProgress = 0
    let segmentCount = 0

    props.data.segments.forEach(segment => {
      if (segment.progress !== undefined) {
        totalProgress += segment.progress
        segmentCount++
      } else {
        // 根据状态估算进度
        switch (segment.status) {
          case '已完成':
            totalProgress += 100
            break
          case '建设中':
            totalProgress += 60 // 默认建设中进度
            break
          case '规划中':
            totalProgress += 0
            break
          default:
            totalProgress += 0
        }
        segmentCount++
      }
    })

    return segmentCount > 0 ? Math.round(totalProgress / segmentCount) : 0
  }

  // 根据状态估算进度
  switch (props.data.status) {
    case '已完成':
      return 100
    case '建设中':
      return 60 // 默认建设中进度
    case '规划中':
      return 0
    default:
      return 0
  }
}

// 获取进度条颜色
const getProgressColor = () => {
  const progress = getConstructionProgress()

  if (progress >= 80) {
    return '#52c41a' // 绿色 - 接近完成
  } else if (progress >= 50) {
    return '#1890ff' // 蓝色 - 正常进度
  } else if (progress >= 20) {
    return '#faad14' // 橙色 - 进度较慢
  } else {
    return '#f5222d' // 红色 - 进度很慢
  }
}



// 处理关闭事件
const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.detail-panel {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 400px;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1001;
  overflow: hidden;
  animation: panelSlideIn 0.3s ease-out;
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 20px;
  margin-right: 8px;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  color: white !important;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.panel-content {
  padding: 20px;
  max-height: calc(80vh - 70px);
  overflow-y: auto;
}

.info-group {
  margin-bottom: 20px;
}

.info-group:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.group-header .el-icon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 6px;
}

.group-header label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.label {
  color: #666;
  font-size: 13px;
  margin-right: 12px;
  min-width: 80px;
}

.value {
  color: #333;
  font-size: 13px;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

/* 电压等级徽章样式 */
.voltage-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.voltage-500kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-220kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-110kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-35kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-10kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 状态徽章样式 */
.status-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.status-completed {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.status-construction {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.status-planning {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.status-maintenance {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.status-shutdown {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.status-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 线路级别徽章样式 */
.level-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.level-international {
  background: linear-gradient(135deg, #ffd666, #ffc53d);
  color: #333;
}

.level-national {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.level-provincial {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.level-strategic {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.level-emergency {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.level-livelihood {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.level-other {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

.level-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 建设进度样式 */
.progress-container {
  margin-top: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.progress-percentage {
  font-size: 14px;
  color: #1890ff;
  font-weight: 600;
}



/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-panel {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    max-height: 70vh;
  }
  
  .panel-content {
    padding: 16px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .value {
    text-align: left;
  }
}
</style>
