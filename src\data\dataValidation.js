/**
 * 数据验证脚本
 * 用于验证电力线路和设施数据的完整性和正确性
 */

import { powerLinesData, YUNNAN_CITIES, INTERNATIONAL_CITIES } from './powerLines.js'
import { powerFacilitiesData, facilityTypes, voltageConfig, statusConfig } from './powerFacilities.js'

// 验证电力线路数据
export const validatePowerLines = () => {
  const results = {
    success: true,
    errors: [],
    summary: {}
  }
  
  try {
    // 验证电压等级数量
    const voltagelevels = Object.keys(powerLinesData)
    if (voltagelevels.length !== 5) {
      results.errors.push(`电压等级数量错误: 期望5个，实际${voltagelevels.length}个`)
      results.success = false
    }
    
    // 验证每个电压等级的线路数量
    voltagelevels.forEach(voltage => {
      const lines = powerLinesData[voltage]
      results.summary[voltage] = lines.length
      
      if (lines.length < 50) {
        results.errors.push(`${voltage}线路数量不足: 期望50+条，实际${lines.length}条`)
        results.success = false
      }
      
      // 验证线路数据结构
      lines.forEach((line, index) => {
        if (!line.id || !line.name || !line.coordinates || !line.status || !line.segments || !line.towers) {
          results.errors.push(`${voltage}第${index + 1}条线路数据结构不完整`)
          results.success = false
        }
        
        // 验证坐标数据
        if (!Array.isArray(line.coordinates) || line.coordinates.length < 2) {
          results.errors.push(`${voltage}第${index + 1}条线路坐标数据无效`)
          results.success = false
        }
        
        // 验证电塔数据
        if (line.towers.length !== line.coordinates.length) {
          results.errors.push(`${voltage}第${index + 1}条线路电塔数量与坐标点数量不匹配`)
          results.success = false
        }
      })
    })
    
    // 验证昆明到万象跨国线路
    const kunmingToVientiane = powerLinesData['500KV'].find(line => 
      line.name.includes('万象') || line.id === '500kv_001'
    )
    
    if (!kunmingToVientiane) {
      results.errors.push('未找到昆明到万象的跨国线路')
      results.success = false
    } else {
      results.summary.kunmingToVientiane = {
        id: kunmingToVientiane.id,
        name: kunmingToVientiane.name,
        segments: kunmingToVientiane.segments.length,
        coordinates: kunmingToVientiane.coordinates.length
      }
    }
    
  } catch (error) {
    results.errors.push(`数据验证过程中发生错误: ${error.message}`)
    results.success = false
  }
  
  return results
}

// 验证电力设施数据
export const validatePowerFacilities = () => {
  const results = {
    success: true,
    errors: [],
    summary: {}
  }
  
  try {
    const { substations, powerPlants, towers } = powerFacilitiesData
    
    // 验证变电站数据
    if (!Array.isArray(substations) || substations.length === 0) {
      results.errors.push('变电站数据无效或为空')
      results.success = false
    } else {
      results.summary.substations = substations.length
      
      // 验证变电站数据结构
      substations.forEach((station, index) => {
        if (!station.id || !station.name || !station.position || !station.voltageLevel || !station.type) {
          results.errors.push(`第${index + 1}个变电站数据结构不完整`)
          results.success = false
        }
      })
    }
    
    // 验证发电站数据
    if (!Array.isArray(powerPlants) || powerPlants.length === 0) {
      results.errors.push('发电站数据无效或为空')
      results.success = false
    } else {
      results.summary.powerPlants = powerPlants.length
      
      // 验证发电站数据结构
      powerPlants.forEach((plant, index) => {
        if (!plant.id || !plant.name || !plant.position || !plant.capacity || !plant.type) {
          results.errors.push(`第${index + 1}个发电站数据结构不完整`)
          results.success = false
        }
      })
    }
    
    // 验证电塔数据
    if (!Array.isArray(towers) || towers.length === 0) {
      results.errors.push('电塔数据无效或为空')
      results.success = false
    } else {
      results.summary.towers = towers.length
      
      // 验证电塔数据结构
      towers.forEach((tower, index) => {
        if (!tower.id || !tower.name || !tower.position || !tower.type) {
          results.errors.push(`第${index + 1}个电塔数据结构不完整`)
          results.success = false
        }
      })
    }
    
  } catch (error) {
    results.errors.push(`设施数据验证过程中发生错误: ${error.message}`)
    results.success = false
  }
  
  return results
}

// 验证配置数据
export const validateConfigurations = () => {
  const results = {
    success: true,
    errors: [],
    summary: {}
  }
  
  try {
    // 验证设施类型配置
    const facilityTypeKeys = Object.keys(facilityTypes)
    results.summary.facilityTypes = facilityTypeKeys
    
    if (!facilityTypeKeys.includes('substation') || !facilityTypeKeys.includes('powerPlant') || !facilityTypeKeys.includes('tower')) {
      results.errors.push('设施类型配置不完整')
      results.success = false
    }
    
    // 验证电压等级配置
    const voltageKeys = Object.keys(voltageConfig)
    results.summary.voltagelevels = voltageKeys
    
    const expectedVoltages = ['500KV', '220KV', '110KV', '35KV', '10KV']
    expectedVoltages.forEach(voltage => {
      if (!voltageKeys.includes(voltage)) {
        results.errors.push(`缺少${voltage}电压等级配置`)
        results.success = false
      }
    })
    
    // 验证状态配置
    const statusKeys = Object.keys(statusConfig)
    results.summary.statuses = statusKeys
    
    const expectedStatuses = ['已完成', '建设中', '规划中']
    expectedStatuses.forEach(status => {
      if (!statusKeys.includes(status)) {
        results.errors.push(`缺少${status}状态配置`)
        results.success = false
      }
    })
    
  } catch (error) {
    results.errors.push(`配置验证过程中发生错误: ${error.message}`)
    results.success = false
  }
  
  return results
}

// 执行完整验证
export const runFullValidation = () => {
  console.log('开始数据验证...\n')
  
  // 验证电力线路数据
  console.log('=== 电力线路数据验证 ===')
  const lineResults = validatePowerLines()
  console.log('线路数量统计:', lineResults.summary)
  
  if (lineResults.success) {
    console.log('✓ 电力线路数据验证通过')
  } else {
    console.log('✗ 电力线路数据验证失败:')
    lineResults.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  // 验证电力设施数据
  console.log('\n=== 电力设施数据验证 ===')
  const facilityResults = validatePowerFacilities()
  console.log('设施数量统计:', facilityResults.summary)
  
  if (facilityResults.success) {
    console.log('✓ 电力设施数据验证通过')
  } else {
    console.log('✗ 电力设施数据验证失败:')
    facilityResults.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  // 验证配置数据
  console.log('\n=== 配置数据验证 ===')
  const configResults = validateConfigurations()
  console.log('配置项统计:', configResults.summary)
  
  if (configResults.success) {
    console.log('✓ 配置数据验证通过')
  } else {
    console.log('✗ 配置数据验证失败:')
    configResults.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  // 总结
  const overallSuccess = lineResults.success && facilityResults.success && configResults.success
  console.log('\n=== 验证总结 ===')
  if (overallSuccess) {
    console.log('✓ 所有数据验证通过，数据文件创建成功！')
  } else {
    console.log('✗ 数据验证存在问题，请检查上述错误信息')
  }
  
  return {
    success: overallSuccess,
    lineResults,
    facilityResults,
    configResults
  }
}
