/**
 * 海量点管理器
 * 负责电塔、变电站、发电站图标的高性能渲染
 * 使用高德地图的MassMarks API优化性能
 */

import { FACILITY_STYLES, PERFORMANCE_CONFIG, ZOOM_THRESHOLDS } from '../data/powerConfig.js'

export class MassMarkerManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    
    // 海量点实例存储
    this.massMarkers = new Map() // 不同类型的海量点实例
    
    // 标记数据存储
    this.markerData = {
      towers: [],
      substations: [],
      powerPlants: []
    }
    
    // 渲染状态
    this.isInitialized = false
    this.renderError = null
    
    // 事件监听器
    this.eventListeners = new Map()

    // 3D模型管理器引用（用于同步筛选状态）
    this.model3DManager = null

    // 电塔筛选状态
    this.towerFiltersEnabled = true

    console.log('MassMarkerManager 初始化完成')
  }

  /**
   * 设置3D模型管理器引用
   * @param {Model3DManager} model3DManager - 3D模型管理器实例
   */
  setModel3DManager(model3DManager) {
    this.model3DManager = model3DManager
    console.log('MassMarkerManager: 已设置3D模型管理器引用')
  }

  /**
   * 初始化海量点
   */
  async initMassMarkers() {
    try {
      console.log('开始初始化海量点...')
      
      // 初始化电塔海量点
      await this.initTowerMassMarkers()
      
      // 初始化变电站海量点
      await this.initSubstationMassMarkers()
      
      // 初始化发电站海量点
      await this.initPowerPlantMassMarkers()
      
      this.isInitialized = true
      this.emit('initialized', this.getInitSummary())
      
      console.log('海量点初始化完成')
      return true
      
    } catch (error) {
      this.renderError = error
      console.error('海量点初始化失败:', error)
      this.emit('initError', error)
      return false
    }
  }

  /**
   * 初始化电塔海量点
   */
  async initTowerMassMarkers() {
    const towerMass = new this.AMap.MassMarks([], {
      opacity: 0.8,
      zIndex: 111,
      cursor: 'pointer',
      style: this.getTowerStyle()
    })
    
    // 添加点击事件
    towerMass.on('click', (e) => {
      this.handleTowerClick(e)
    })
    
    this.massMarkers.set('towers', towerMass)
    this.map.add(towerMass)
    
    console.log('电塔海量点初始化完成')
  }

  /**
   * 初始化变电站海量点
   */
  async initSubstationMassMarkers() {
    const substationMass = new this.AMap.MassMarks([], {
      opacity: 0.9,
      zIndex: 112,
      cursor: 'pointer',
      style: this.getSubstationStyle()
    })
    
    // 添加点击事件
    substationMass.on('click', (e) => {
      this.handleSubstationClick(e)
    })
    
    this.massMarkers.set('substations', substationMass)
    this.map.add(substationMass)
    
    console.log('变电站海量点初始化完成')
  }

  /**
   * 初始化发电站海量点
   */
  async initPowerPlantMassMarkers() {
    const powerPlantMass = new this.AMap.MassMarks([], {
      opacity: 0.9,
      zIndex: 113,
      cursor: 'pointer',
      style: this.getPowerPlantStyle()
    })
    
    // 添加点击事件
    powerPlantMass.on('click', (e) => {
      this.handlePowerPlantClick(e)
    })
    
    this.massMarkers.set('powerPlants', powerPlantMass)
    this.map.add(powerPlantMass)
    
    console.log('发电站海量点初始化完成')
  }

  /**
   * 更新电塔标记数据
   * @param {Array} linesData - 线路数据（包含电塔位置）
   */
  updateTowerMarkers(linesData) {
    const towers = []

    // 从线路数据中提取电塔位置
    Object.values(linesData).forEach(lines => {
      lines.forEach(line => {
        if (line.towers) {
          line.towers.forEach(tower => {
            towers.push({
              lnglat: tower.position,
              name: tower.id,
              id: tower.id,
              style: 0, // 使用默认样式
              extData: {
                type: 'tower',
                towerId: tower.id,
                powerlineName: line.name,
                voltageLevel: line.voltageLevel,
                detailInfo: tower.detailInfo
              }
            })
          })
        }
      })
    })

    // 应用防重叠算法
    const optimizedTowers = this.applyAntiOverlapAlgorithm(towers, 'towers')

    // 更新海量点数据
    const towerMass = this.massMarkers.get('towers')
    if (towerMass) {
      // 存储数据
      this.markerData.towers = optimizedTowers

      // 初始显示所有电塔标记（后续会根据3D模型加载情况调整）
      towerMass.setData(optimizedTowers)

      console.log(`电塔标记更新完成: ${optimizedTowers.length} 个，初始状态：显示`)
    }
  }

  /**
   * 更新变电站标记数据
   * @param {Array} substations - 变电站数据
   */
  updateSubstationMarkers(substations) {
    const markers = substations.map(station => ({
      lnglat: station.position,
      name: station.name,
      id: station.id,
      style: this.getSubstationStyleIndex(station.type),
      extData: {
        type: 'substation',
        substationType: station.type || 'substation', // 添加变电站类型
        stationId: station.id,
        stationName: station.name,
        voltageLevel: station.voltageLevel,
        capacity: station.capacity,
        detailInfo: station.detailInfo
      }
    }))
    
    // 应用防重叠算法
    const optimizedMarkers = this.applyAntiOverlapAlgorithm(markers, 'substations')

    // 更新海量点数据
    const substationMass = this.massMarkers.get('substations')
    if (substationMass) {
      substationMass.setData(optimizedMarkers)
      this.markerData.substations = optimizedMarkers
      console.log(`变电站标记更新完成: ${optimizedMarkers.length} 个`)
    }
  }

  /**
   * 更新发电站标记数据
   * @param {Array} powerPlants - 发电站数据
   */
  updatePowerPlantMarkers(powerPlants) {
    const markers = powerPlants.map(plant => ({
      lnglat: plant.position,
      name: plant.name,
      id: plant.id,
      style: this.getPowerPlantStyleIndex(plant.type),
      extData: {
        type: 'powerPlant',
        plantId: plant.id,
        plantName: plant.name,
        capacity: plant.capacity,
        generationType: plant.type,
        detailInfo: plant.detailInfo
      }
    }))
    
    // 应用防重叠算法
    const optimizedMarkers = this.applyAntiOverlapAlgorithm(markers, 'powerPlants')

    // 更新海量点数据
    const powerPlantMass = this.massMarkers.get('powerPlants')
    if (powerPlantMass) {
      powerPlantMass.setData(optimizedMarkers)
      this.markerData.powerPlants = optimizedMarkers
      console.log(`发电站标记更新完成: ${optimizedMarkers.length} 个`)
    }
  }

  /**
   * 防重叠算法 - 增强版
   * @param {Array} markers - 标记数组
   * @param {string} markerType - 标记类型 ('towers', 'substations', 'powerPlants')
   * @returns {Array} 优化后的标记数组
   */
  applyAntiOverlapAlgorithm(markers, markerType = 'default') {
    // 根据标记类型设置不同的最小距离
    const minDistances = {
      towers: 0.0008,        // 电塔间距约80米
      substations: 0.002,    // 变电站间距约200米
      powerPlants: 0.003,    // 发电站间距约300米
      default: 0.001         // 默认100米
    }

    const minDistance = minDistances[markerType] || minDistances.default
    const optimized = []

    // 获取所有现有标记位置（跨类型检查）
    const allExistingPositions = this.getAllExistingPositions()

    markers.forEach(marker => {
      let position = [...marker.lnglat] // 创建副本
      let attempts = 0
      const maxAttempts = 20 // 增加尝试次数

      // 检查是否与现有标记重叠（包括其他类型的标记）
      while (attempts < maxAttempts) {
        const hasOverlap = this.checkOverlapWithAll(position, optimized, allExistingPositions, minDistance)

        if (!hasOverlap) {
          break
        }

        // 使用更智能的位置调整策略
        position = this.adjustPosition(marker.lnglat, attempts, minDistance)
        attempts++
      }

      optimized.push({
        ...marker,
        lnglat: position
      })
    })

    return optimized
  }

  /**
   * 获取所有现有标记位置
   * @returns {Array} 所有现有标记位置数组
   */
  getAllExistingPositions() {
    const positions = []

    // 收集所有类型的现有标记位置
    this.markerData.towers?.forEach(marker => positions.push(marker.lnglat))
    this.markerData.substations?.forEach(marker => positions.push(marker.lnglat))
    this.markerData.powerPlants?.forEach(marker => positions.push(marker.lnglat))

    return positions
  }

  /**
   * 检查与所有标记的重叠
   * @param {Array} position - 检查的位置
   * @param {Array} optimized - 已优化的标记
   * @param {Array} allExisting - 所有现有位置
   * @param {number} minDistance - 最小距离
   * @returns {boolean} 是否有重叠
   */
  checkOverlapWithAll(position, optimized, allExisting, minDistance) {
    // 检查与当前批次已优化标记的重叠
    const overlapWithOptimized = optimized.some(existing => {
      const distance = this.calculateDistance(position, existing.lnglat)
      return distance < minDistance
    })

    // 检查与所有现有标记的重叠
    const overlapWithExisting = allExisting.some(existingPos => {
      const distance = this.calculateDistance(position, existingPos)
      return distance < minDistance
    })

    return overlapWithOptimized || overlapWithExisting
  }

  /**
   * 智能位置调整
   * @param {Array} originalPos - 原始位置
   * @param {number} attempt - 尝试次数
   * @param {number} minDistance - 最小距离
   * @returns {Array} 调整后的位置
   */
  adjustPosition(originalPos, attempt, minDistance) {
    // 使用螺旋式调整策略，而不是随机调整
    const angle = (attempt * 45) % 360 // 每次旋转45度
    const radius = minDistance * (1 + attempt * 0.5) // 逐渐增大半径

    const radians = (angle * Math.PI) / 180
    const deltaX = Math.cos(radians) * radius
    const deltaY = Math.sin(radians) * radius

    return [
      originalPos[0] + deltaX,
      originalPos[1] + deltaY
    ]
  }

  /**
   * 计算两点间距离
   * @param {Array} pos1 - 位置1 [lng, lat]
   * @param {Array} pos2 - 位置2 [lng, lat]
   * @returns {number} 距离
   */
  calculateDistance(pos1, pos2) {
    const dx = pos1[0] - pos2[0]
    const dy = pos1[1] - pos2[1]
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 获取电塔样式
   * @returns {Array} 样式数组
   */
  getTowerStyle() {
    return [{
      url: '/src/assets/icon/tower.svg',
      anchor: new this.AMap.Pixel(12, 12),
      size: new this.AMap.Size(24, 24)
    }]
  }

  /**
   * 获取变电站样式
   * @returns {Array} 样式数组
   */
  getSubstationStyle() {
    return [
      {
        // 变电站
        url: '/src/assets/icon/substation.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 换流站
        url: '/src/assets/icon/converter.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      }
    ]
  }

  /**
   * 获取发电站样式
   * @returns {Array} 样式数组
   */
  getPowerPlantStyle() {
    return [
      {
        // 火电/燃煤发电
        url: '/src/assets/icon/thermal.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 风电
        url: '/src/assets/icon/wind.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 水电
        url: '/src/assets/icon/hydro.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 太阳能发电
        url: '/src/assets/icon/solar.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },

    ]
  }

  /**
   * 获取变电站样式索引
   * @param {string} type - 变电站类型
   * @returns {number} 样式索引
   */
  getSubstationStyleIndex(type) {
    switch (type) {
      case 'converter_station':
        return 1
      case 'substation':
      default:
        return 0
    }
  }

  /**
   * 获取电塔样式
   * @returns {Array} 样式数组
   */
  getTowerStyle() {
    return [
      {
        // 电塔
        url: '/src/assets/icon/tower.svg',
        anchor: new this.AMap.Pixel(12, 12),
        size: new this.AMap.Size(24, 24)
      }
    ]
  }

  /**
   * 获取电塔样式索引
   * @param {string} type - 电塔类型
   * @returns {number} 样式索引
   */
  getTowerStyleIndex(type) {
    return 0 // 目前只有一种电塔样式
  }

  /**
   * 获取发电站样式索引
   * @param {string} type - 发电站类型
   * @returns {number} 样式索引
   */
  getPowerPlantStyleIndex(type) {
    switch (type) {
      case 'wind':
        return 1
      case 'hydro':
        return 2
      case 'solar':
        return 3
      case 'thermal':
      default:
        return 0 // 火电/燃煤发电作为默认
    }
  }

  /**
   * 处理电塔点击事件
   * @param {Object} e - 事件对象
   */
  handleTowerClick(e) {
    const data = e.data.extData
    this.emit('towerClick', {
      type: 'tower',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 处理变电站点击事件
   * @param {Object} e - 事件对象
   */
  handleSubstationClick(e) {
    const data = e.data.extData
    this.emit('substationClick', {
      type: 'substation',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 处理发电站点击事件
   * @param {Object} e - 事件对象
   */
  handlePowerPlantClick(e) {
    const data = e.data.extData
    this.emit('powerPlantClick', {
      type: 'powerPlant',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 获取初始化摘要
   * @returns {Object} 初始化摘要
   */
  getInitSummary() {
    return {
      isInitialized: this.isInitialized,
      massMarkersCount: this.massMarkers.size,
      markerCounts: {
        towers: this.markerData.towers.length,
        substations: this.markerData.substations.length,
        powerPlants: this.markerData.powerPlants.length
      },
      renderError: this.renderError
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 根据缩放级别和3D模式控制电塔标记的显示/隐藏
   * @param {number} currentZoom - 当前缩放级别
   * @param {string} viewMode - 视图模式 ('2D' 或 '3D')
   * @param {boolean} has3DModelsLoaded - 是否有3D模型已加载（可选参数）
   */
  controlTowerMarkersVisibility(currentZoom, viewMode, has3DModelsLoaded = false) {
    try {
      const towerMassMarker = this.massMarkers.get('towers')
      if (!towerMassMarker) {
        return
      }

      // 首先检查筛选状态
      if (!this.towerFiltersEnabled) {
        // 如果电塔筛选被禁用，直接隐藏所有2D标记
        towerMassMarker.setData([])
        console.log('电塔筛选已禁用：隐藏所有电塔2D标记')
        return
      }

      // 检查是否达到显示电塔的最小缩放级别
      if (currentZoom < ZOOM_THRESHOLDS.SHOW_TOWERS) {
        // 缩放级别太小，不显示电塔
        towerMassMarker.setData([])
        console.log(`缩放级别 ${currentZoom} 小于最小显示级别 ${ZOOM_THRESHOLDS.SHOW_TOWERS}：隐藏电塔`)
        return
      }

      // 改进的显示逻辑：
      // 1. 在2D模式下，根据缩放级别控制电塔密度
      // 2. 在3D模式下，只有当缩放级别足够且确实有3D模型加载成功时，才隐藏2D图标
      // 3. 如果3D模型加载失败，继续显示2D图标作为备用

      let shouldShowTowerMarkers = true
      let towersToShow = this.markerData.towers

      if (viewMode === '3D' && currentZoom >= ZOOM_THRESHOLDS.SHOW_3D_MODELS) {
        // 在3D模式且缩放级别足够时，检查是否有3D模型加载
        // 如果有3D模型加载成功，则隐藏2D图标；否则继续显示2D图标
        if (has3DModelsLoaded) {
          shouldShowTowerMarkers = false
          console.log(`缩放级别 ${currentZoom}，3D模式，3D模型已加载：隐藏电塔2D标记`)
        } else {
          console.log(`缩放级别 ${currentZoom}，3D模式，3D模型未加载：继续显示电塔2D标记`)
          // 根据缩放级别控制电塔密度
          towersToShow = this.getFilteredTowersByZoom(currentZoom)
        }
      } else {
        console.log(`缩放级别 ${currentZoom}，${viewMode}模式：显示电塔2D标记`)
        // 根据缩放级别控制电塔密度
        towersToShow = this.getFilteredTowersByZoom(currentZoom)
      }

      if (shouldShowTowerMarkers) {
        // 显示电塔标记（可能经过密度筛选）
        towerMassMarker.setData(towersToShow)
        console.log(`显示 ${towersToShow.length} 个电塔标记（总共 ${this.markerData.towers.length} 个）`)
      } else {
        // 隐藏电塔标记
        towerMassMarker.setData([])
      }
    } catch (error) {
      console.error('控制电塔标记显示失败:', error)
    }
  }

  /**
   * 根据缩放级别筛选电塔，控制显示密度
   * @param {number} currentZoom - 当前缩放级别
   * @returns {Array} 筛选后的电塔数组
   */
  getFilteredTowersByZoom(currentZoom) {
    if (!this.markerData.towers || this.markerData.towers.length === 0) {
      return []
    }

    // 如果缩放级别达到显示所有电塔的阈值，返回所有电塔
    if (currentZoom >= ZOOM_THRESHOLDS.SHOW_ALL_TOWERS) {
      return this.markerData.towers
    }

    // 根据缩放级别计算显示密度
    // 缩放级别越小，显示的电塔越少
    let densityRatio = 1.0

    if (currentZoom >= 11) {
      densityRatio = 0.8 // 显示80%的电塔
    } else if (currentZoom >= 10.5) {
      densityRatio = 0.6 // 显示60%的电塔
    } else if (currentZoom >= 10) {
      densityRatio = 0.4 // 显示40%的电塔
    } else {
      densityRatio = 0.2 // 显示20%的电塔
    }

    // 使用固定的间隔来选择电塔，确保分布均匀
    const step = Math.max(1, Math.floor(1 / densityRatio))
    const filteredTowers = this.markerData.towers.filter((tower, index) => {
      return index % step === 0
    })

    console.log(`缩放级别 ${currentZoom}：密度比例 ${densityRatio}，显示 ${filteredTowers.length}/${this.markerData.towers.length} 个电塔`)
    return filteredTowers
  }

  /**
   * 设置电塔标记的可见性
   * @param {boolean} visible - 是否可见
   */
  setTowerMarkersVisible(visible) {
    try {
      const towerMassMarker = this.massMarkers.get('towers')
      if (!towerMassMarker) {
        return
      }

      if (visible) {
        // 根据当前缩放级别显示适当密度的电塔
        const currentZoom = this.map.getZoom()
        const towersToShow = this.getFilteredTowersByZoom(currentZoom)
        towerMassMarker.setData(towersToShow)
        console.log(`电塔标记已显示：${towersToShow.length} 个`)
      } else {
        towerMassMarker.setData([])
        console.log('电塔标记已隐藏')
      }
    } catch (error) {
      console.error('设置电塔标记可见性失败:', error)
    }
  }

  /**
   * 清除所有标记
   */
  clearAllMarkers() {
    // 清空所有海量点数据
    this.massMarkers.forEach(massMarker => {
      massMarker.setData([])
    })

    // 清空数据存储
    this.markerData = { towers: [], substations: [], powerPlants: [] }

    console.log('所有标记已清除')
  }

  /**
   * 应用变电站筛选
   * @param {Array} activeFilters - 激活的变电站类型筛选器
   */
  async applySubstationFilters(activeFilters) {
    try {
      console.log('应用变电站筛选:', activeFilters)

      const substationMass = this.massMarkers.get('substations')
      if (!substationMass) {
        console.warn('变电站海量点实例不存在')
        return
      }

      // 如果没有筛选条件，隐藏所有变电站
      if (!activeFilters || activeFilters.length === 0) {
        substationMass.setData([])
        console.log('变电站筛选：隐藏所有变电站')
        return
      }

      // 根据筛选条件过滤变电站数据
      const filteredSubstations = this.markerData.substations.filter(marker => {
        const substationType = marker.extData?.substationType || 'substation'
        return activeFilters.includes(substationType)
      })

      // 更新显示
      substationMass.setData(filteredSubstations)
      console.log(`变电站筛选完成: 显示 ${filteredSubstations.length}/${this.markerData.substations.length} 个变电站`)

    } catch (error) {
      console.error('应用变电站筛选失败:', error)
    }
  }

  /**
   * 应用发电站筛选
   * @param {Array} activeFilters - 激活的发电站类型筛选器
   */
  async applyPowerPlantFilters(activeFilters) {
    try {
      console.log('应用发电站筛选:', activeFilters)

      const powerPlantMass = this.massMarkers.get('powerPlants')
      if (!powerPlantMass) {
        console.warn('发电站海量点实例不存在')
        return
      }

      // 如果没有筛选条件，隐藏所有发电站
      if (!activeFilters || activeFilters.length === 0) {
        powerPlantMass.setData([])
        console.log('发电站筛选：隐藏所有发电站')
        return
      }

      // 根据筛选条件过滤发电站数据
      const filteredPowerPlants = this.markerData.powerPlants.filter(marker => {
        const plantType = marker.extData?.generationType || marker.extData?.type
        return activeFilters.includes(plantType)
      })

      // 更新显示
      powerPlantMass.setData(filteredPowerPlants)
      console.log(`发电站筛选完成: 显示 ${filteredPowerPlants.length}/${this.markerData.powerPlants.length} 个发电站`)

    } catch (error) {
      console.error('应用发电站筛选失败:', error)
    }
  }

  /**
   * 应用电塔筛选
   * @param {Array} activeFilters - 激活的电塔类型筛选器
   */
  async applyTowerFilters(activeFilters) {
    try {
      console.log('🔄 应用电塔筛选:', activeFilters)

      // 记录之前的筛选状态
      const previousState = this.towerFiltersEnabled

      // 更新筛选状态
      this.towerFiltersEnabled = activeFilters && activeFilters.length > 0 && activeFilters.includes('tower')
      console.log(`📊 电塔筛选状态变化: ${previousState ? '启用' : '禁用'} -> ${this.towerFiltersEnabled ? '启用' : '禁用'}`)

      const towerMass = this.massMarkers.get('towers')
      if (!towerMass) {
        console.warn('⚠️ 电塔海量点实例不存在')
        return
      }

      // 如果状态发生变化，需要特殊处理3D模型
      const stateChanged = previousState !== this.towerFiltersEnabled

      // 同时控制3D模型显示
      if (this.model3DManager) {
        if (!this.towerFiltersEnabled) {
          // 禁用电塔显示，强制清理所有3D模型
          console.log('🚫 电塔筛选：强制清理所有3D模型')
          this.model3DManager.clear3DModels() // 使用clear而不是hide，确保彻底清理
        } else if (stateChanged) {
          // 从禁用变为启用，需要强制刷新
          console.log('🔄 电塔筛选：状态变化，强制刷新3D模型')

          // 先清理现有模型，避免重复
          this.model3DManager.clear3DModels()

          // 延迟一点时间再重新加载，确保清理完成
          setTimeout(() => {
            const shouldLoad = this.model3DManager.shouldLoad3DModels()
            console.log('🔍 电塔筛选：检查3D模型显示条件:', shouldLoad)
            console.log('- 当前缩放:', this.map.getZoom())
            console.log('- 视图模式:', this.getViewMode())
            console.log('- 筛选状态:', this.towerFiltersEnabled)

            if (shouldLoad) {
              console.log('✅ 电塔筛选：重新管理3D模型')
              this.model3DManager.manage3DModels()
            }
          }, 100) // 短暂延迟确保清理完成
        } else {
          // 状态未变化且为启用状态，不需要重复管理3D模型
          console.log('🔄 电塔筛选：状态未变化，跳过3D模型管理（避免重复加载）')
          // 注释掉这行，避免重复加载模型
          // this.model3DManager.manage3DModels()
        }
      }

      // 根据筛选状态控制2D标记显示
      if (!this.towerFiltersEnabled) {
        // 隐藏所有电塔2D标记
        towerMass.setData([])
        console.log('🚫 电塔筛选：隐藏所有电塔2D标记')
      } else {
        // 显示电塔2D标记（但需要考虑3D模型状态）
        // 这里调用现有的控制逻辑，它会根据缩放级别和3D模型状态决定是否显示2D标记
        const currentZoom = this.map.getZoom()
        const viewMode = this.getViewMode()
        const has3DModelsLoaded = this.model3DManager ? this.model3DManager.hasSuccessfullyLoadedModels : false

        this.controlTowerMarkersVisibility(currentZoom, viewMode, has3DModelsLoaded)
        console.log('✅ 电塔筛选：根据当前状态控制2D标记显示')
      }

      console.log(`✅ 电塔筛选完成: ${this.towerFiltersEnabled ? '显示' : '隐藏'} 电塔`)

    } catch (error) {
      console.error('❌ 应用电塔筛选失败:', error)
    }
  }

  /**
   * 重置所有设施筛选（显示所有设施）
   */
  resetAllFacilityFilters() {
    try {
      console.log('重置所有设施筛选')

      // 重置变电站显示
      const substationMass = this.massMarkers.get('substations')
      if (substationMass && this.markerData.substations.length > 0) {
        substationMass.setData(this.markerData.substations)
      }

      // 重置发电站显示
      const powerPlantMass = this.massMarkers.get('powerPlants')
      if (powerPlantMass && this.markerData.powerPlants.length > 0) {
        powerPlantMass.setData(this.markerData.powerPlants)
      }

      // 重置电塔显示
      const towerMass = this.massMarkers.get('towers')
      if (towerMass && this.markerData.towers.length > 0) {
        towerMass.setData(this.markerData.towers)
      }

      console.log('所有设施筛选已重置')
    } catch (error) {
      console.error('重置设施筛选失败:', error)
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 从地图中移除所有海量点
    this.massMarkers.forEach(massMarker => {
      this.map.remove(massMarker)
    })

    this.massMarkers.clear()
    this.markerData = { towers: [], substations: [], powerPlants: [] }
    this.eventListeners.clear()
    this.map = null
    this.AMap = null

    console.log('MassMarkerManager 已销毁')
  }

  /**
   * 获取地图视图模式
   * @returns {string} 视图模式
   */
  getViewMode() {
    try {
      if (typeof this.map.getViewMode === 'function') {
        return this.map.getViewMode()
      } else if (typeof this.map.getViewMode_ === 'function') {
        return this.map.getViewMode_()
      } else {
        // 通过其他方式判断是否为3D模式
        const pitch = this.map.getPitch ? this.map.getPitch() : 0
        return pitch > 0 ? '3D' : '2D'
      }
    } catch (error) {
      console.warn('获取视图模式失败:', error)
      return '2D'
    }
  }
}

export default MassMarkerManager
